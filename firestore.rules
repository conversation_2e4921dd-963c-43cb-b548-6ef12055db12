rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection rules
    match /users/{userId} {
      // Users can read other users' basic profile information for search and social features
      allow read: if request.auth != null;

      // Users can only write to their own user document
      allow write: if request.auth != null && request.auth.uid == userId;
    }

    // Changelog collection - allow authenticated users to read and write
    match /changelog/{changelogId} {
      allow read: if request.auth != null;
      allow create, update: if request.auth != null;
      allow delete: if request.auth != null;
    }

    // Stories collection rules
    match /stories/{storyId} {
      // Authenticated users can read all stories (we'll filter published stories in the app)
      allow read: if request.auth != null;

      // Only authenticated users can create stories
      allow create: if request.auth != null
        && request.auth.uid == request.resource.data.author
        && request.resource.data.author is string
        && request.resource.data.title is string
        && request.resource.data.content is string;

      // Only the author can update or delete their own stories
      allow update, delete: if request.auth != null
        && request.auth.uid == resource.data.author;
    }

    // Support requests - users can create and read their own
    match /supportRequests/{requestId} {
      allow read, write: if request.auth != null
        && (request.auth.uid == resource.data.authorId
            || request.auth.uid == request.resource.data.authorId);
      allow read: if request.auth != null; // Allow reading all for community support
    }

    // Activism campaigns - users can create and read
    match /activismCampaigns/{campaignId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null
        && request.auth.uid == request.resource.data.creatorId;
      allow update: if request.auth != null
        && request.auth.uid == resource.data.creatorId;
    }

    // Notifications - users can read their own notifications
    match /notifications/{notificationId} {
      allow read, write: if request.auth != null
        && request.auth.uid == resource.data.userId;
    }

    // Messages - users can read/write messages they're part of
    match /messages/{messageId} {
      allow read, write: if request.auth != null
        && (request.auth.uid in resource.data.participants
            || request.auth.uid in request.resource.data.participants);
    }

    // Connections - users can manage their own connections
    match /connections/{connectionId} {
      allow read, write: if request.auth != null
        && (request.auth.uid == resource.data.fromUserId
            || request.auth.uid == resource.data.toUserId
            || request.auth.uid == request.resource.data.fromUserId
            || request.auth.uid == request.resource.data.toUserId);
    }

    // Friend requests - users can manage requests they're involved in
    match /friendRequests/{requestId} {
      // Allow reading if user is involved in the request
      allow read: if request.auth != null
        && (request.auth.uid == resource.data.fromUserId
            || request.auth.uid == resource.data.toUserId);

      // Allow creating if user is the sender (fromUserId)
      allow create: if request.auth != null
        && request.auth.uid == request.resource.data.fromUserId;

      // Allow updating if user is involved in the request
      allow update: if request.auth != null
        && (request.auth.uid == resource.data.fromUserId
            || request.auth.uid == resource.data.toUserId);

      // Allow deleting if user is involved in the request
      allow delete: if request.auth != null
        && (request.auth.uid == resource.data.fromUserId
            || request.auth.uid == resource.data.toUserId);
    }

    // User privacy settings - users can read and write their own privacy settings
    match /userPrivacy/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // User presence - users can read others' presence for social features, write their own
    match /userPresence/{userId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }

    // Friendships - users can read and manage friendships they're part of
    match /friendships/{friendshipId} {
      allow read: if request.auth != null
        && request.auth.uid in resource.data.users;
      allow create: if request.auth != null
        && request.auth.uid in request.resource.data.users;
      allow update, delete: if request.auth != null
        && request.auth.uid in resource.data.users;
    }

    // User roles collection - for admin system
    match /userRoles/{userId} {
      // Users can read their own role
      allow read: if request.auth != null && request.auth.uid == userId;

      // Platform owner can read and write any role
      allow read, write: if request.auth != null
        && request.auth.token.email == '<EMAIL>';

      // Allow initial role assignment for platform owner (for bootstrap)
      allow create: if request.auth != null
        && request.auth.token.email == '<EMAIL>'
        && userId == request.auth.uid;

      // Allow system to assign platform owner role during initialization
      allow write: if request.auth != null
        && request.auth.token.email == '<EMAIL>'
        && userId == request.auth.uid
        && request.resource.data.role == 'platform_owner';
    }

    // Admin actions log - for tracking admin activities
    match /adminActions/{actionId} {
      // Platform owner and admins can read all admin actions
      allow read: if request.auth != null
        && request.auth.token.email == '<EMAIL>';

      // Allow creating admin action logs
      allow create: if request.auth != null
        && request.auth.token.email == '<EMAIL>';
    }

    // Default deny rule for any other collections
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
