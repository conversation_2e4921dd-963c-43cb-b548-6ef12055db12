import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react({
      // Enable React Fast Refresh for better development experience
      fastRefresh: true,
      // Optimize JSX runtime for production
      jsxRuntime: 'automatic'
    })
  ],

  // Build optimization for production
  build: {
    // Output directory (default is 'dist')
    outDir: 'dist',

    // Generate source maps for debugging in production (smaller maps for performance)
    sourcemap: 'hidden',

    // Optimize bundle size
    rollupOptions: {
      output: {
        // Manual chunk splitting for better caching and performance
        manualChunks: (id) => {
          // Vendor chunk for React and related libraries
          if (id.includes('node_modules')) {
            if (id.includes('react') || id.includes('react-dom') || id.includes('react-router')) {
              return 'vendor-react';
            }
            // Firebase chunk for Firebase-related code
            if (id.includes('firebase')) {
              return 'vendor-firebase';
            }
            // Other vendor libraries
            return 'vendor-other';
          }
          // Component chunks for lazy-loaded components
          if (id.includes('components/DynamicNewsfeed') ||
              id.includes('components/MessagingSystem') ||
              id.includes('components/NotificationCenter')) {
            return 'components-heavy';
          }
          if (id.includes('components/kids/')) {
            return 'components-kids';
          }
        },
        // Optimize asset file names for better caching
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split('.');
          const ext = info[info.length - 1];
          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
            return `assets/images/[name]-[hash][extname]`;
          }
          if (/css/i.test(ext)) {
            return `assets/css/[name]-[hash][extname]`;
          }
          return `assets/[name]-[hash][extname]`;
        },
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js'
      }
    },

    // Minify for production with optimized settings
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // Remove console.log in production
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug']
      }
    },

    // Target modern browsers for better optimization
    target: 'es2020',

    // Chunk size warning limit
    chunkSizeWarningLimit: 500,

    // Enable CSS code splitting
    cssCodeSplit: true,

    // Optimize dependencies
    commonjsOptions: {
      include: [/node_modules/]
    }
  },

  // Development server configuration
  server: {
    port: 3000,
    open: true,
    // Enable HMR for better development experience
    hmr: true,
    // Optimize dependencies during development
    optimizeDeps: {
      include: ['react', 'react-dom', 'react-router-dom', 'firebase/app', 'firebase/auth', 'firebase/firestore']
    }
  },

  // Preview server configuration
  preview: {
    port: 4173,
    open: true
  },

  // Define global constants
  define: {
    // Ensure process.env is available for compatibility
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'production')
  },

  // Optimize dependencies
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'firebase/app',
      'firebase/auth',
      'firebase/firestore'
    ],
    exclude: ['@netlify/plugin-lighthouse']
  },

  // Enable experimental features for better performance
  experimental: {
    renderBuiltUrl(filename, { hostType }) {
      if (hostType === 'js') {
        return { js: `/${filename}` }
      } else {
        return { css: `/${filename}` }
      }
    }
  }
})
