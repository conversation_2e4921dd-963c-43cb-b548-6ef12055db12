{"indexes": [{"collectionGroup": "stories", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isPublished", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "stories", "queryScope": "COLLECTION", "fields": [{"fieldPath": "author", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "stories", "queryScope": "COLLECTION", "fields": [{"fieldPath": "topic", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "participants", "arrayConfig": "CONTAINS"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}], "fieldOverrides": []}