import { db } from '../firebase';
import {
  doc,
  setDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  collection,
  query,
  where,
  orderBy,
  limit,
  onSnapshot,
  serverTimestamp,
  arrayUnion,
  arrayRemove,
  writeBatch
} from 'firebase/firestore';
import { createNotification } from './notifications';
import {
  canSendFriendRequest,
  shouldAppearInSearch,
  shouldAppearInSuggestions
} from './privacy';

/**
 * Friends & Social Connections Service
 * Handles friend requests, connections, and social relationships
 */

/**
 * Send a friend request to another user
 * @param {string} fromUserId - ID of user sending the request
 * @param {string} toUserId - ID of user receiving the request
 * @param {string} message - Optional message with the request
 * @returns {Object} Result object with success status
 */
export async function sendFriendRequest(fromUserId, toUserId, message = '') {
  try {
    console.log('🤝 Starting friend request process:', { fromUserId, toUserId, message });

    if (fromUserId === toUserId) {
      throw new Error('Cannot send friend request to yourself');
    }

    // Check privacy permissions
    console.log('🔒 Checking permissions...');
    const permission = await canSendFriendRequest(fromUserId, toUserId);
    console.log('🔒 Permission check result:', permission);
    if (!permission.allowed) {
      throw new Error(permission.reason);
    }

    // Check if request already exists
    console.log('📋 Checking for existing requests...');
    const existingRequest = await getFriendRequestStatus(fromUserId, toUserId);
    console.log('📋 Existing request check:', existingRequest);
    if (existingRequest) {
      throw new Error('Friend request already exists or users are already friends');
    }

    // Validate message requirement
    if (permission.requireMessage && !message.trim()) {
      throw new Error('This user requires a message with friend requests');
    }

    // Create unique request ID
    const requestId = `${fromUserId}_${toUserId}`;
    console.log('📝 Creating friend request with ID:', requestId);

    const requestData = {
      id: requestId,
      fromUserId,
      toUserId,
      message: message.trim(),
      status: permission.autoAccept ? 'accepted' : 'pending',
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };

    console.log('💾 Saving friend request data:', requestData);
    await setDoc(doc(db, 'friendRequests', requestId), requestData);
    console.log('✅ Friend request saved successfully');

    // If auto-accept, create friendship immediately
    if (permission.autoAccept) {
      const batch = writeBatch(db);

      // Create friendship record
      const friendshipId = [fromUserId, toUserId].sort().join('_');
      batch.set(doc(db, 'friendships', friendshipId), {
        users: [fromUserId, toUserId],
        createdAt: serverTimestamp(),
        status: 'active'
      });

      // Update user friend lists
      batch.update(doc(db, 'users', fromUserId), {
        friends: arrayUnion(toUserId),
        friendCount: (await getUserFriendCount(fromUserId)) + 1
      });

      batch.update(doc(db, 'users', toUserId), {
        friends: arrayUnion(fromUserId),
        friendCount: (await getUserFriendCount(toUserId)) + 1
      });

      await batch.commit();
    }

    // Get sender info for notification
    const senderDoc = await getDoc(doc(db, 'users', fromUserId));
    const senderName = senderDoc.exists() ?
      (senderDoc.data().name || senderDoc.data().email) :
      'Someone';

    // Send notification to recipient
    const notificationMessage = permission.autoAccept
      ? `${senderName} is now your friend!`
      : `${senderName} sent you a friend request${message ? `: "${message}"` : ''}`;

    await createNotification({
      userId: toUserId,
      type: permission.autoAccept ? 'friend_request_accepted' : 'friend_request',
      title: permission.autoAccept ? 'New Friend' : 'New Friend Request',
      message: notificationMessage,
      actionUrl: permission.autoAccept ? `/friends` : `/friends/requests`,
      actionData: { fromUserId, requestId },
      priority: 'normal',
      category: 'social'
    });

    return {
      success: true,
      requestId,
      autoAccepted: permission.autoAccept
    };
  } catch (error) {
    console.error('❌ Error sending friend request:', error);
    console.error('❌ Error details:', {
      code: error.code,
      message: error.message,
      stack: error.stack
    });
    throw error;
  }
}

/**
 * Accept a friend request
 * @param {string} requestId - ID of the friend request
 * @param {string} userId - ID of user accepting the request
 * @returns {Object} Result object with success status
 */
export async function acceptFriendRequest(requestId, userId) {
  try {
    const requestDoc = await getDoc(doc(db, 'friendRequests', requestId));
    
    if (!requestDoc.exists()) {
      throw new Error('Friend request not found');
    }

    const requestData = requestDoc.data();
    
    if (requestData.toUserId !== userId) {
      throw new Error('Unauthorized to accept this request');
    }

    if (requestData.status !== 'pending') {
      throw new Error('Request is no longer pending');
    }

    const batch = writeBatch(db);

    // Update request status
    batch.update(doc(db, 'friendRequests', requestId), {
      status: 'accepted',
      acceptedAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    // Create friendship record
    const friendshipId = [requestData.fromUserId, requestData.toUserId].sort().join('_');
    batch.set(doc(db, 'friendships', friendshipId), {
      users: [requestData.fromUserId, requestData.toUserId],
      createdAt: serverTimestamp(),
      status: 'active'
    });

    // Update user friend lists
    batch.update(doc(db, 'users', requestData.fromUserId), {
      friends: arrayUnion(requestData.toUserId),
      friendCount: (await getUserFriendCount(requestData.fromUserId)) + 1
    });

    batch.update(doc(db, 'users', requestData.toUserId), {
      friends: arrayUnion(requestData.fromUserId),
      friendCount: (await getUserFriendCount(requestData.toUserId)) + 1
    });

    await batch.commit();

    // Get accepter info for notification
    const accepterDoc = await getDoc(doc(db, 'users', userId));
    const accepterName = accepterDoc.exists() ? 
      (accepterDoc.data().name || accepterDoc.data().email) : 
      'Someone';

    // Notify the original sender
    await createNotification({
      userId: requestData.fromUserId,
      type: 'friend_request_accepted',
      title: 'Friend Request Accepted',
      message: `${accepterName} accepted your friend request!`,
      actionUrl: `/friends`,
      actionData: { friendId: userId },
      priority: 'normal',
      category: 'social'
    });

    return { success: true, friendshipId };
  } catch (error) {
    console.error('Error accepting friend request:', error);
    throw error;
  }
}

/**
 * Decline a friend request
 * @param {string} requestId - ID of the friend request
 * @param {string} userId - ID of user declining the request
 * @returns {Object} Result object with success status
 */
export async function declineFriendRequest(requestId, userId) {
  try {
    const requestDoc = await getDoc(doc(db, 'friendRequests', requestId));
    
    if (!requestDoc.exists()) {
      throw new Error('Friend request not found');
    }

    const requestData = requestDoc.data();
    
    if (requestData.toUserId !== userId) {
      throw new Error('Unauthorized to decline this request');
    }

    if (requestData.status !== 'pending') {
      throw new Error('Request is no longer pending');
    }

    await updateDoc(doc(db, 'friendRequests', requestId), {
      status: 'declined',
      declinedAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    return { success: true };
  } catch (error) {
    console.error('Error declining friend request:', error);
    throw error;
  }
}

/**
 * Cancel a sent friend request
 * @param {string} requestId - ID of the friend request
 * @param {string} userId - ID of user who sent the request
 * @returns {Object} Result object with success status
 */
export async function cancelFriendRequest(requestId, userId) {
  try {
    const requestDoc = await getDoc(doc(db, 'friendRequests', requestId));
    
    if (!requestDoc.exists()) {
      throw new Error('Friend request not found');
    }

    const requestData = requestDoc.data();
    
    if (requestData.fromUserId !== userId) {
      throw new Error('Unauthorized to cancel this request');
    }

    if (requestData.status !== 'pending') {
      throw new Error('Request is no longer pending');
    }

    await deleteDoc(doc(db, 'friendRequests', requestId));

    return { success: true };
  } catch (error) {
    console.error('Error canceling friend request:', error);
    throw error;
  }
}

/**
 * Remove a friend (unfriend)
 * @param {string} userId - Current user's ID
 * @param {string} friendId - Friend's ID to remove
 * @returns {Object} Result object with success status
 */
export async function removeFriend(userId, friendId) {
  try {
    const friendshipId = [userId, friendId].sort().join('_');
    const batch = writeBatch(db);

    // Remove friendship record
    batch.delete(doc(db, 'friendships', friendshipId));

    // Update user friend lists
    batch.update(doc(db, 'users', userId), {
      friends: arrayRemove(friendId),
      friendCount: Math.max(0, (await getUserFriendCount(userId)) - 1)
    });

    batch.update(doc(db, 'users', friendId), {
      friends: arrayRemove(userId),
      friendCount: Math.max(0, (await getUserFriendCount(friendId)) - 1)
    });

    await batch.commit();

    return { success: true };
  } catch (error) {
    console.error('Error removing friend:', error);
    throw error;
  }
}

/**
 * Get friend request status between two users
 * @param {string} userId1 - First user ID
 * @param {string} userId2 - Second user ID
 * @returns {Object|null} Request data or null if no request exists
 */
export async function getFriendRequestStatus(userId1, userId2) {
  try {
    // Check both possible request directions
    const request1 = await getDoc(doc(db, 'friendRequests', `${userId1}_${userId2}`));
    const request2 = await getDoc(doc(db, 'friendRequests', `${userId2}_${userId1}`));
    
    if (request1.exists()) {
      return { id: request1.id, ...request1.data() };
    }
    
    if (request2.exists()) {
      return { id: request2.id, ...request2.data() };
    }

    // Check if they're already friends
    const friendshipId = [userId1, userId2].sort().join('_');
    const friendship = await getDoc(doc(db, 'friendships', friendshipId));
    
    if (friendship.exists()) {
      return { status: 'friends', ...friendship.data() };
    }

    return null;
  } catch (error) {
    console.error('Error getting friend request status:', error);
    return null;
  }
}

/**
 * Get user's friend count
 * @param {string} userId - User ID
 * @returns {number} Number of friends
 */
async function getUserFriendCount(userId) {
  try {
    const userDoc = await getDoc(doc(db, 'users', userId));
    return userDoc.exists() ? (userDoc.data().friendCount || 0) : 0;
  } catch (error) {
    console.error('Error getting friend count:', error);
    return 0;
  }
}

/**
 * Get pending friend requests for a user
 * @param {string} userId - User ID
 * @param {string} type - 'received' or 'sent'
 * @returns {Array} Array of friend requests
 */
export async function getPendingFriendRequests(userId, type = 'received') {
  try {
    const field = type === 'received' ? 'toUserId' : 'fromUserId';
    const requestsQuery = query(
      collection(db, 'friendRequests'),
      where(field, '==', userId),
      where('status', '==', 'pending'),
      orderBy('createdAt', 'desc')
    );

    const snapshot = await getDocs(requestsQuery);
    const requests = [];

    for (const doc of snapshot.docs) {
      const requestData = { id: doc.id, ...doc.data() };

      // Get user info for the other person
      const otherUserId = type === 'received' ? requestData.fromUserId : requestData.toUserId;
      const userDoc = await getDoc(doc(db, 'users', otherUserId));

      if (userDoc.exists()) {
        requestData.userInfo = userDoc.data();
      }

      requests.push(requestData);
    }

    return requests;
  } catch (error) {
    console.error('Error getting pending friend requests:', error);
    return [];
  }
}

/**
 * Get user's friends list
 * @param {string} userId - User ID
 * @param {number} limitCount - Maximum number of friends to return
 * @returns {Array} Array of friends with their info
 */
export async function getUserFriends(userId, limitCount = 50) {
  try {
    const userDoc = await getDoc(doc(db, 'users', userId));

    if (!userDoc.exists() || !userDoc.data().friends) {
      return [];
    }

    const friendIds = userDoc.data().friends.slice(0, limitCount);
    const friends = [];

    for (const friendId of friendIds) {
      const friendDoc = await getDoc(doc(db, 'users', friendId));
      if (friendDoc.exists()) {
        friends.push({
          id: friendId,
          ...friendDoc.data()
        });
      }
    }

    return friends;
  } catch (error) {
    console.error('Error getting user friends:', error);
    return [];
  }
}

/**
 * Subscribe to friend requests updates
 * @param {string} userId - User ID to monitor
 * @param {Function} callback - Callback function for updates
 * @param {string} type - 'received' or 'sent'
 * @returns {Function} Unsubscribe function
 */
export function subscribeToFriendRequests(userId, callback, type = 'received') {
  try {
    const field = type === 'received' ? 'toUserId' : 'fromUserId';
    const requestsQuery = query(
      collection(db, 'friendRequests'),
      where(field, '==', userId),
      where('status', '==', 'pending'),
      orderBy('createdAt', 'desc')
    );

    return onSnapshot(requestsQuery, async (snapshot) => {
      const requests = [];

      for (const doc of snapshot.docs) {
        const requestData = { id: doc.id, ...doc.data() };

        // Get user info for the other person
        const otherUserId = type === 'received' ? requestData.fromUserId : requestData.toUserId;
        const userDoc = await getDoc(doc(db, 'users', otherUserId));

        if (userDoc.exists()) {
          requestData.userInfo = userDoc.data();
        }

        requests.push(requestData);
      }

      callback(requests);
    });
  } catch (error) {
    console.error('Error subscribing to friend requests:', error);
    return () => {};
  }
}

/**
 * Subscribe to user's friends list updates
 * @param {string} userId - User ID to monitor
 * @param {Function} callback - Callback function for updates
 * @returns {Function} Unsubscribe function
 */
export function subscribeToUserFriends(userId, callback) {
  try {
    return onSnapshot(doc(db, 'users', userId), async (doc) => {
      if (doc.exists() && doc.data().friends) {
        const friendIds = doc.data().friends;
        const friends = [];

        for (const friendId of friendIds) {
          const friendDoc = await getDoc(doc(db, 'users', friendId));
          if (friendDoc.exists()) {
            friends.push({
              id: friendId,
              ...friendDoc.data()
            });
          }
        }

        callback(friends);
      } else {
        callback([]);
      }
    });
  } catch (error) {
    console.error('Error subscribing to user friends:', error);
    return () => {};
  }
}

/**
 * Search for users to send friend requests to
 * @param {string} searchTerm - Search term (name or email)
 * @param {string} currentUserId - Current user's ID to exclude from results
 * @param {number} limitCount - Maximum number of results
 * @returns {Array} Array of user search results
 */
export async function searchUsers(searchTerm, currentUserId, limitCount = 20) {
  try {
    if (!searchTerm.trim()) return [];

    const searchTermLower = searchTerm.toLowerCase().trim();
    const users = [];

    console.log('🔍 Starting user search for:', searchTermLower);

    // First, try to search by email directly if the search term looks like an email
    if (searchTermLower.includes('@')) {
      try {
        console.log('📧 Searching by email:', searchTermLower);
        const emailQuery = query(
          collection(db, 'users'),
          where('email', '==', searchTermLower),
          limit(5)
        );

        const emailSnapshot = await getDocs(emailQuery);
        console.log('📧 Email search results count:', emailSnapshot.docs.length);

        for (const doc of emailSnapshot.docs) {
          const userData = doc.data();
          const userId = doc.id;

          console.log('👤 Found user by email:', { userId, email: userData.email, name: userData.name });

          // Skip current user
          if (userId === currentUserId) {
            console.log('⏭️ Skipping current user');
            continue;
          }

          // Check privacy settings
          const canAppearInSearch = await shouldAppearInSearch(userId, currentUserId);
          console.log('🔒 Privacy check for user', userId, ':', canAppearInSearch);
          if (!canAppearInSearch) continue;

          // Get friend request status
          const requestStatus = await getFriendRequestStatus(currentUserId, userId);
          console.log('🤝 Friend request status:', requestStatus);

          users.push({
            id: userId,
            ...userData,
            friendRequestStatus: requestStatus
          });
        }
      } catch (emailError) {
        console.error('❌ Email search failed:', emailError);
        console.log('🔄 Falling back to general search');
      }
    }

    // If we haven't found enough users, do a general search
    if (users.length < limitCount) {
      try {
        console.log('🔍 Starting general search, current results:', users.length);
        // Get all users and filter client-side for better search capability
        const allUsersQuery = query(
          collection(db, 'users'),
          limit(200) // Reasonable limit to avoid performance issues
        );

        const allUsersSnapshot = await getDocs(allUsersQuery);
        console.log('📊 Total users in database:', allUsersSnapshot.docs.length);

        for (const doc of allUsersSnapshot.docs) {
          const userData = doc.data();
          const userId = doc.id;

          // Skip current user
          if (userId === currentUserId) continue;

          // Skip if already found in email search
          if (users.some(user => user.id === userId)) continue;

          // Check privacy settings
          const canAppearInSearch = await shouldAppearInSearch(userId, currentUserId);
          if (!canAppearInSearch) continue;

          // Check if name or email matches search term (case-insensitive)
          const name = userData.name || '';
          const email = userData.email || '';

          if (name.toLowerCase().includes(searchTermLower) ||
              email.toLowerCase().includes(searchTermLower)) {

            console.log('✅ Found matching user:', { userId, name, email });

            // Get friend request status
            const requestStatus = await getFriendRequestStatus(currentUserId, userId);

            users.push({
              id: userId,
              ...userData,
              friendRequestStatus: requestStatus
            });

            // Stop when we have enough results
            if (users.length >= limitCount) break;
          }
        }
      } catch (generalError) {
        console.error('❌ General search failed:', generalError);
      }
    }

    console.log('🎯 Search completed. Final results:', users.length, 'users found');
    return users.slice(0, limitCount);
  } catch (error) {
    console.error('❌ Error searching users:', error);
    return [];
  }
}

/**
 * Generate intelligent friend suggestions based on multiple factors
 * @param {string} currentUserId - Current user's ID
 * @param {number} limitCount - Maximum number of suggestions
 * @returns {Array} Array of suggested users with relevance scores
 */
export async function getFriendSuggestions(currentUserId, limitCount = 10) {
  try {
    // Get current user's profile
    const currentUserDoc = await getDoc(doc(db, 'users', currentUserId));
    if (!currentUserDoc.exists()) {
      return [];
    }

    const currentUserData = currentUserDoc.data();
    const currentUserFriends = currentUserData.friends || [];

    // Get all users except current user and existing friends
    const usersQuery = query(collection(db, 'users'));
    const usersSnapshot = await getDocs(usersQuery);

    const suggestions = [];

    for (const userDoc of usersSnapshot.docs) {
      const userId = userDoc.id;
      const userData = userDoc.data();

      // Skip current user and existing friends
      if (userId === currentUserId || currentUserFriends.includes(userId)) {
        continue;
      }

      // Check privacy settings - should this user appear in suggestions?
      const canAppearInSuggestions = await shouldAppearInSuggestions(userId, currentUserId);
      if (!canAppearInSuggestions) continue;

      // Check if there's already a pending friend request
      const requestStatus = await getFriendRequestStatus(currentUserId, userId);
      if (requestStatus && (requestStatus.status === 'pending' || requestStatus.status === 'friends')) {
        continue;
      }

      // Calculate relevance score based on multiple factors
      const relevanceScore = await calculateRelevanceScore(currentUserData, userData, currentUserId, userId);

      if (relevanceScore > 0) {
        suggestions.push({
          id: userId,
          ...userData,
          relevanceScore,
          friendRequestStatus: requestStatus,
          suggestionReasons: await getSuggestionReasons(currentUserData, userData, currentUserId, userId)
        });
      }
    }

    // Sort by relevance score and return top suggestions
    return suggestions
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, limitCount);

  } catch (error) {
    console.error('Error getting friend suggestions:', error);
    return [];
  }
}

/**
 * Calculate relevance score between current user and potential friend
 * @param {Object} currentUserData - Current user's profile data
 * @param {Object} targetUserData - Target user's profile data
 * @param {string} currentUserId - Current user's ID
 * @param {string} targetUserId - Target user's ID
 * @returns {number} Relevance score (0-100)
 */
async function calculateRelevanceScore(currentUserData, targetUserData, currentUserId, targetUserId) {
  let score = 0;

  // 1. Shared Interests (25 points max)
  const currentInterests = currentUserData.interests || [];
  const targetInterests = targetUserData.interests || [];
  const sharedInterests = currentInterests.filter(interest =>
    targetInterests.some(tInterest =>
      tInterest.toLowerCase().includes(interest.toLowerCase()) ||
      interest.toLowerCase().includes(tInterest.toLowerCase())
    )
  );
  score += Math.min(25, sharedInterests.length * 8);

  // 2. Similar Cultural Values (20 points max)
  const currentRoleModel = (currentUserData.roleModel || '').toLowerCase();
  const targetRoleModel = (targetUserData.roleModel || '').toLowerCase();
  const currentTradition = (currentUserData.tradition || '').toLowerCase();
  const targetTradition = (targetUserData.tradition || '').toLowerCase();

  if (currentRoleModel && targetRoleModel &&
      (currentRoleModel.includes(targetRoleModel) || targetRoleModel.includes(currentRoleModel))) {
    score += 10;
  }

  if (currentTradition && targetTradition &&
      (currentTradition.includes(targetTradition) || targetTradition.includes(currentTradition))) {
    score += 10;
  }

  // 3. Similar Goals/Dreams (15 points max)
  const currentDream = (currentUserData.dream || '').toLowerCase();
  const targetDream = (targetUserData.dream || '').toLowerCase();
  const currentGoals = (currentUserData.goals || '').toLowerCase();
  const targetGoals = (targetUserData.goals || '').toLowerCase();

  if (currentDream && targetDream &&
      (currentDream.includes(targetDream) || targetDream.includes(currentDream))) {
    score += 8;
  }

  if (currentGoals && targetGoals &&
      (currentGoals.includes(targetGoals) || targetGoals.includes(currentGoals))) {
    score += 7;
  }

  // 4. Mutual Friends (20 points max)
  const mutualFriends = await getMutualFriendsCount(currentUserId, targetUserId);
  score += Math.min(20, mutualFriends * 4);

  // 5. Similar Activity Level (10 points max)
  const currentActivity = currentUserData.totalStories || 0;
  const targetActivity = targetUserData.totalStories || 0;
  const activityDiff = Math.abs(currentActivity - targetActivity);
  const maxActivity = Math.max(currentActivity, targetActivity);

  if (maxActivity > 0) {
    const activitySimilarity = 1 - (activityDiff / (maxActivity + 1));
    score += activitySimilarity * 10;
  }

  // 6. Recent Activity (5 points max)
  const targetLastActivity = new Date(targetUserData.lastActivity || 0);
  const daysSinceActivity = (Date.now() - targetLastActivity.getTime()) / (1000 * 60 * 60 * 24);

  if (daysSinceActivity < 7) {
    score += 5;
  } else if (daysSinceActivity < 30) {
    score += 3;
  } else if (daysSinceActivity < 90) {
    score += 1;
  }

  // 7. Mentorship Match (5 points max)
  if (currentUserData.mentorAvailable && !targetUserData.mentorAvailable) {
    score += 3; // Current user can mentor target
  } else if (!currentUserData.mentorAvailable && targetUserData.mentorAvailable) {
    score += 3; // Target can mentor current user
  } else if (currentUserData.mentorAvailable && targetUserData.mentorAvailable) {
    score += 2; // Both are mentors - good for peer mentoring
  }

  return Math.round(score);
}

/**
 * Get count of mutual friends between two users
 * @param {string} userId1 - First user ID
 * @param {string} userId2 - Second user ID
 * @returns {number} Number of mutual friends
 */
async function getMutualFriendsCount(userId1, userId2) {
  try {
    const user1Doc = await getDoc(doc(db, 'users', userId1));
    const user2Doc = await getDoc(doc(db, 'users', userId2));

    if (!user1Doc.exists() || !user2Doc.exists()) {
      return 0;
    }

    const user1Friends = user1Doc.data().friends || [];
    const user2Friends = user2Doc.data().friends || [];

    const mutualFriends = user1Friends.filter(friendId => user2Friends.includes(friendId));
    return mutualFriends.length;
  } catch (error) {
    console.error('Error getting mutual friends count:', error);
    return 0;
  }
}

/**
 * Get list of mutual friends between two users
 * @param {string} userId1 - First user ID
 * @param {string} userId2 - Second user ID
 * @returns {Array} Array of mutual friend user data
 */
export async function getMutualFriends(userId1, userId2) {
  try {
    const user1Doc = await getDoc(doc(db, 'users', userId1));
    const user2Doc = await getDoc(doc(db, 'users', userId2));

    if (!user1Doc.exists() || !user2Doc.exists()) {
      return [];
    }

    const user1Friends = user1Doc.data().friends || [];
    const user2Friends = user2Doc.data().friends || [];

    const mutualFriendIds = user1Friends.filter(friendId => user2Friends.includes(friendId));
    const mutualFriends = [];

    for (const friendId of mutualFriendIds) {
      const friendDoc = await getDoc(doc(db, 'users', friendId));
      if (friendDoc.exists()) {
        mutualFriends.push({
          id: friendId,
          ...friendDoc.data()
        });
      }
    }

    return mutualFriends;
  } catch (error) {
    console.error('Error getting mutual friends:', error);
    return [];
  }
}

/**
 * Generate human-readable suggestion reasons
 * @param {Object} currentUserData - Current user's profile data
 * @param {Object} targetUserData - Target user's profile data
 * @param {string} currentUserId - Current user's ID
 * @param {string} targetUserId - Target user's ID
 * @returns {Array} Array of suggestion reason strings
 */
async function getSuggestionReasons(currentUserData, targetUserData, currentUserId, targetUserId) {
  const reasons = [];

  // Shared interests
  const currentInterests = currentUserData.interests || [];
  const targetInterests = targetUserData.interests || [];
  const sharedInterests = currentInterests.filter(interest =>
    targetInterests.some(tInterest =>
      tInterest.toLowerCase().includes(interest.toLowerCase()) ||
      interest.toLowerCase().includes(tInterest.toLowerCase())
    )
  );

  if (sharedInterests.length > 0) {
    if (sharedInterests.length === 1) {
      reasons.push(`You both are interested in ${sharedInterests[0]}`);
    } else if (sharedInterests.length === 2) {
      reasons.push(`You both share interests in ${sharedInterests.join(' and ')}`);
    } else {
      reasons.push(`You share ${sharedInterests.length} common interests including ${sharedInterests.slice(0, 2).join(', ')} and more`);
    }
  }

  // Similar role models
  const currentRoleModel = (currentUserData.roleModel || '').toLowerCase();
  const targetRoleModel = (targetUserData.roleModel || '').toLowerCase();
  if (currentRoleModel && targetRoleModel &&
      (currentRoleModel.includes(targetRoleModel) || targetRoleModel.includes(currentRoleModel))) {
    reasons.push(`You both admire similar role models`);
  }

  // Similar traditions/values
  const currentTradition = (currentUserData.tradition || '').toLowerCase();
  const targetTradition = (targetUserData.tradition || '').toLowerCase();
  if (currentTradition && targetTradition &&
      (currentTradition.includes(targetTradition) || targetTradition.includes(currentTradition))) {
    reasons.push(`You share similar cultural values and traditions`);
  }

  // Similar goals/dreams
  const currentDream = (currentUserData.dream || '').toLowerCase();
  const targetDream = (targetUserData.dream || '').toLowerCase();
  if (currentDream && targetDream &&
      (currentDream.includes(targetDream) || targetDream.includes(currentDream))) {
    reasons.push(`You have similar dreams and aspirations`);
  }

  // Mutual friends
  const mutualFriendsCount = await getMutualFriendsCount(currentUserId, targetUserId);
  if (mutualFriendsCount > 0) {
    if (mutualFriendsCount === 1) {
      reasons.push(`You have 1 mutual friend`);
    } else {
      reasons.push(`You have ${mutualFriendsCount} mutual friends`);
    }
  }

  // Activity level
  const currentActivity = currentUserData.totalStories || 0;
  const targetActivity = targetUserData.totalStories || 0;
  if (currentActivity > 0 && targetActivity > 0) {
    const avgActivity = (currentActivity + targetActivity) / 2;
    if (avgActivity >= 5) {
      reasons.push(`Both active community members`);
    } else if (avgActivity >= 2) {
      reasons.push(`Both engaged in sharing stories`);
    }
  }

  // Recent activity
  const targetLastActivity = new Date(targetUserData.lastActivity || 0);
  const daysSinceActivity = (Date.now() - targetLastActivity.getTime()) / (1000 * 60 * 60 * 24);
  if (daysSinceActivity < 7) {
    reasons.push(`Recently active in the community`);
  }

  // Mentorship opportunities
  if (currentUserData.mentorAvailable && !targetUserData.mentorAvailable) {
    reasons.push(`You could be a mentor to them`);
  } else if (!currentUserData.mentorAvailable && targetUserData.mentorAvailable) {
    reasons.push(`They could be a mentor to you`);
  } else if (currentUserData.mentorAvailable && targetUserData.mentorAvailable) {
    reasons.push(`Both interested in mentorship`);
  }

  // Join date proximity (new members connecting)
  const currentJoinDate = new Date(currentUserData.joinDate || 0);
  const targetJoinDate = new Date(targetUserData.joinDate || 0);
  const daysDiff = Math.abs(currentJoinDate - targetJoinDate) / (1000 * 60 * 60 * 24);
  if (daysDiff < 30) {
    reasons.push(`Both new to the NAROOP community`);
  }

  return reasons.slice(0, 3); // Return top 3 reasons to avoid overwhelming
}

/**
 * Get friend suggestions based on story interactions
 * @param {string} currentUserId - Current user's ID
 * @param {number} limitCount - Maximum number of suggestions
 * @returns {Array} Array of users who interacted with similar stories
 */
export async function getStoryBasedSuggestions(currentUserId, limitCount = 5) {
  try {
    // Get stories that current user has reacted to
    const storiesQuery = query(collection(db, 'stories'));
    const storiesSnapshot = await getDocs(storiesQuery);

    const userInteractions = new Map(); // userId -> interaction count

    storiesSnapshot.docs.forEach(storyDoc => {
      const storyData = storyDoc.data();

      // Check if current user reacted to this story
      const currentUserReacted = (
        (storyData.heartBy && storyData.heartBy.includes(currentUserId)) ||
        (storyData.clapBy && storyData.clapBy.includes(currentUserId))
      );

      if (currentUserReacted) {
        // Find other users who also reacted to this story
        const allReactors = [
          ...(storyData.heartBy || []),
          ...(storyData.clapBy || [])
        ];

        allReactors.forEach(userId => {
          if (userId !== currentUserId) {
            userInteractions.set(userId, (userInteractions.get(userId) || 0) + 1);
          }
        });
      }
    });

    // Get user data for top interactors
    const suggestions = [];
    const sortedInteractions = Array.from(userInteractions.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, limitCount);

    for (const [userId, interactionCount] of sortedInteractions) {
      const userDoc = await getDoc(doc(db, 'users', userId));
      if (userDoc.exists()) {
        const userData = userDoc.data();

        // Check privacy settings for story-based suggestions
        const canAppearInSuggestions = await shouldAppearInSuggestions(userId, currentUserId);
        if (!canAppearInSuggestions) continue;

        // Check if already friends or has pending request
        const requestStatus = await getFriendRequestStatus(currentUserId, userId);
        if (!requestStatus || (requestStatus.status !== 'friends' && requestStatus.status !== 'pending')) {
          suggestions.push({
            id: userId,
            ...userData,
            interactionCount,
            suggestionReason: `You both liked ${interactionCount} similar ${interactionCount === 1 ? 'story' : 'stories'}`,
            friendRequestStatus: requestStatus
          });
        }
      }
    }

    return suggestions;
  } catch (error) {
    console.error('Error getting story-based suggestions:', error);
    return [];
  }
}

/**
 * Get comprehensive friend suggestions combining multiple algorithms
 * @param {string} currentUserId - Current user's ID
 * @param {number} limitCount - Maximum number of suggestions
 * @returns {Array} Array of suggested users from multiple sources
 */
export async function getComprehensiveFriendSuggestions(currentUserId, limitCount = 15) {
  try {
    // Get suggestions from different algorithms
    const [
      profileBasedSuggestions,
      storyBasedSuggestions
    ] = await Promise.all([
      getFriendSuggestions(currentUserId, Math.ceil(limitCount * 0.7)),
      getStoryBasedSuggestions(currentUserId, Math.ceil(limitCount * 0.3))
    ]);

    // Combine and deduplicate suggestions
    const allSuggestions = new Map();

    // Add profile-based suggestions
    profileBasedSuggestions.forEach(user => {
      allSuggestions.set(user.id, {
        ...user,
        suggestionType: 'profile',
        combinedScore: user.relevanceScore || 0
      });
    });

    // Add story-based suggestions (merge if user already exists)
    storyBasedSuggestions.forEach(user => {
      if (allSuggestions.has(user.id)) {
        const existing = allSuggestions.get(user.id);
        existing.combinedScore += (user.interactionCount * 5); // Boost score for story interactions
        existing.suggestionReasons = [
          ...(existing.suggestionReasons || []),
          user.suggestionReason
        ];
        existing.suggestionType = 'combined';
      } else {
        allSuggestions.set(user.id, {
          ...user,
          suggestionType: 'story',
          combinedScore: user.interactionCount * 10,
          suggestionReasons: [user.suggestionReason]
        });
      }
    });

    // Convert to array and sort by combined score
    return Array.from(allSuggestions.values())
      .sort((a, b) => b.combinedScore - a.combinedScore)
      .slice(0, limitCount);

  } catch (error) {
    console.error('Error getting comprehensive friend suggestions:', error);
    return [];
  }
}
