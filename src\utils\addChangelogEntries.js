/**
 * Direct changelog entry addition script
 * Run this in the browser console to add changelog entries
 */

import { db } from '../firebase';
import { collection, addDoc, getDocs, query, where } from 'firebase/firestore';

export const addChangelogEntries = async () => {
  try {
    console.log('🚀 Adding changelog entries...');
    
    const changelogRef = collection(db, 'changelog');
    
    // Check if v1.1.1 already exists
    const v111Query = query(changelogRef, where('version', '==', '1.1.1'));
    const v111Snapshot = await getDocs(v111Query);
    
    if (v111Snapshot.empty) {
      // Add v1.1.1 entry
      const v111Entry = {
        version: '1.1.1',
        title: 'Critical Bug Fixes - Story Submission & Mobile Navigation 🔧',
        description: 'We\'ve fixed several important issues that were affecting your experience on NAROOP. Story sharing now works smoothly, and mobile navigation is much improved!',
        category: 'bugfix',
        releaseDate: new Date().toISOString(),
        changes: [
          'Fixed story submission errors - you can now share your stories without permission issues',
          'Improved mobile menu - all navigation options are now visible and scrollable on mobile devices',
          'Enhanced form validation with clearer error messages',
          'Better authentication handling to prevent login-related issues',
          'Improved mobile touch targets for easier navigation on phones and tablets',
          'Fixed duplicate Kids Zone display issue for cleaner navigation',
          'Added better error handling throughout the platform',
          'Enhanced security with updated Firebase rules'
        ],
        isBreaking: false,
        timestamp: Date.now()
      };
      
      await addDoc(changelogRef, v111Entry);
      console.log('✅ Added v1.1.1 changelog entry');
    } else {
      console.log('ℹ️ v1.1.1 entry already exists');
    }
    
    // Check if v1.1.0 already exists
    const v110Query = query(changelogRef, where('version', '==', '1.1.0'));
    const v110Snapshot = await getDocs(v110Query);
    
    if (v110Snapshot.empty) {
      // Add v1.1.0 entry
      const v110Entry = {
        version: '1.1.0',
        title: 'Fresh New Look - Green Theme Update! 🌱',
        description: 'We\'ve given NAROOP a beautiful new look! The login page and overall design now features a fresh green color scheme that represents growth, prosperity, and positive energy.',
        category: 'improvement',
        releaseDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
        changes: [
          'Updated login page with a beautiful green color theme',
          'Replaced orange and purple colors with calming green tones',
          'New green background gradients throughout the platform',
          'Improved visual consistency across all pages',
          'Enhanced user experience with nature-inspired colors',
          'Better color accessibility and readability'
        ],
        isBreaking: false,
        timestamp: Date.now() - 1 * 24 * 60 * 60 * 1000
      };
      
      await addDoc(changelogRef, v110Entry);
      console.log('✅ Added v1.1.0 changelog entry');
    } else {
      console.log('ℹ️ v1.1.0 entry already exists');
    }
    
    // Add v1.0.0 initial release entry
    const v100Query = query(changelogRef, where('version', '==', '1.0.0'));
    const v100Snapshot = await getDocs(v100Query);
    
    if (v100Snapshot.empty) {
      const v100Entry = {
        version: '1.0.0',
        title: 'Welcome to NAROOP! 🎉',
        description: 'The official launch of NAROOP - Narrative of Our People. A platform dedicated to sharing positive stories and building community within the Black community.',
        category: 'feature',
        releaseDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
        changes: [
          'Story sharing platform with rich text editor',
          'Community features for connecting with others',
          'Kids Zone with age-appropriate content',
          'User authentication and profiles',
          'Mobile-responsive design',
          'Task management and goal setting',
          'Messaging system for community interaction',
          'Newsfeed for discovering new stories'
        ],
        isBreaking: false,
        timestamp: Date.now() - 7 * 24 * 60 * 60 * 1000
      };
      
      await addDoc(changelogRef, v100Entry);
      console.log('✅ Added v1.0.0 changelog entry');
    } else {
      console.log('ℹ️ v1.0.0 entry already exists');
    }
    
    console.log('🎉 All changelog entries added successfully!');
    return { success: true, message: 'Changelog entries added successfully' };
    
  } catch (error) {
    console.error('❌ Error adding changelog entries:', error);
    return { success: false, error: error.message };
  }
};

// Make it available globally for console access
window.addChangelogEntries = addChangelogEntries;
