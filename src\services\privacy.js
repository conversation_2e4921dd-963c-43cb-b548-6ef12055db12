import { db } from '../firebase';
import {
  doc,
  getDoc,
  updateDoc,
  setDoc,
  serverTimestamp
} from 'firebase/firestore';

/**
 * Privacy Service
 * Handles user privacy settings and controls for connections, profile visibility, and interactions
 */

// Default privacy settings
export const DEFAULT_PRIVACY_SETTINGS = {
  // Friend Request Settings
  friendRequests: {
    whoCanSendRequests: 'everyone', // 'everyone', 'friends_of_friends', 'no_one'
    requireMessage: false, // Require a message with friend requests
    autoAcceptFromMutualFriends: false // Auto-accept requests from users with mutual friends
  },
  
  // Profile Visibility Settings
  profileVisibility: {
    whoCanSeeProfile: 'community', // 'everyone', 'community', 'friends_only', 'private'
    whoCanSeeStories: 'community', // 'everyone', 'community', 'friends_only', 'private'
    whoCanSeeActivity: 'friends_only', // 'everyone', 'community', 'friends_only', 'private'
    whoCanSeeFriends: 'friends_only', // 'everyone', 'community', 'friends_only', 'private'
    showOnlineStatus: true, // Show online/offline status
    showLastSeen: true // Show last seen timestamp
  },
  
  // Search & Discovery Settings
  searchability: {
    allowInSearch: true, // Allow profile to appear in search results
    allowInSuggestions: true, // Allow profile to appear in friend suggestions
    allowStoryBasedSuggestions: true // Allow suggestions based on story interactions
  },
  
  // Communication Settings
  communication: {
    whoCanMessage: 'friends_only', // 'everyone', 'friends_only', 'no_one'
    whoCanMention: 'friends_only', // 'everyone', 'friends_only', 'no_one'
    allowGroupInvites: true, // Allow invitations to group chats
    allowEventInvites: true // Allow invitations to community events
  },
  
  // Notification Settings
  notifications: {
    friendRequestNotifications: true,
    messageNotifications: true,
    mentionNotifications: true,
    activityNotifications: false,
    suggestionNotifications: true
  },
  
  // Data & Analytics
  dataSettings: {
    allowAnalytics: true, // Allow usage analytics for platform improvement
    allowPersonalization: true, // Allow data use for personalized features
    allowThirdPartyIntegration: false // Allow integration with external services
  },
  
  // Metadata
  lastUpdated: null,
  version: '1.0'
};

/**
 * Get user's privacy settings
 * @param {string} userId - User ID
 * @returns {Object} Privacy settings object
 */
export async function getUserPrivacySettings(userId) {
  try {
    const privacyDoc = await getDoc(doc(db, 'userPrivacy', userId));
    
    if (privacyDoc.exists()) {
      const settings = privacyDoc.data();
      // Merge with defaults to ensure all settings exist
      return mergeWithDefaults(settings);
    } else {
      // Create default privacy settings for new user
      const defaultSettings = {
        ...DEFAULT_PRIVACY_SETTINGS,
        lastUpdated: serverTimestamp()
      };
      
      await setDoc(doc(db, 'userPrivacy', userId), defaultSettings);
      return defaultSettings;
    }
  } catch (error) {
    console.error('Error getting privacy settings:', error);
    return DEFAULT_PRIVACY_SETTINGS;
  }
}

/**
 * Update user's privacy settings
 * @param {string} userId - User ID
 * @param {Object} updates - Privacy settings updates
 * @returns {Object} Result object
 */
export async function updatePrivacySettings(userId, updates) {
  try {
    const privacyRef = doc(db, 'userPrivacy', userId);
    
    // Get current settings
    const currentSettings = await getUserPrivacySettings(userId);
    
    // Deep merge updates with current settings
    const updatedSettings = deepMerge(currentSettings, {
      ...updates,
      lastUpdated: serverTimestamp(),
      version: '1.0'
    });
    
    await updateDoc(privacyRef, updatedSettings);
    
    return { success: true, settings: updatedSettings };
  } catch (error) {
    console.error('Error updating privacy settings:', error);
    throw error;
  }
}

/**
 * Check if user can send friend request to target user
 * @param {string} fromUserId - User sending the request
 * @param {string} toUserId - User receiving the request
 * @returns {Object} Permission result with allowed status and reason
 */
export async function canSendFriendRequest(fromUserId, toUserId) {
  try {
    if (fromUserId === toUserId) {
      return { allowed: false, reason: 'Cannot send friend request to yourself' };
    }

    // Get target user's privacy settings
    const targetPrivacy = await getUserPrivacySettings(toUserId);
    const friendRequestSettings = targetPrivacy.friendRequests;

    // Check friend request permissions
    switch (friendRequestSettings.whoCanSendRequests) {
      case 'no_one':
        return { allowed: false, reason: 'This user is not accepting friend requests' };
      
      case 'friends_of_friends':
        const hasMutualFriends = await checkMutualFriends(fromUserId, toUserId);
        if (!hasMutualFriends) {
          return { allowed: false, reason: 'This user only accepts requests from friends of friends' };
        }
        break;
      
      case 'everyone':
      default:
        // Allow request
        break;
    }

    return { 
      allowed: true, 
      requireMessage: friendRequestSettings.requireMessage,
      autoAccept: friendRequestSettings.autoAcceptFromMutualFriends && await checkMutualFriends(fromUserId, toUserId)
    };
  } catch (error) {
    console.error('Error checking friend request permission:', error);
    return { allowed: false, reason: 'Unable to verify permissions' };
  }
}

/**
 * Check if user can view another user's profile
 * @param {string} viewerId - User viewing the profile
 * @param {string} profileUserId - User whose profile is being viewed
 * @returns {Object} Permission result with visibility levels
 */
export async function canViewProfile(viewerId, profileUserId) {
  try {
    if (viewerId === profileUserId) {
      return { 
        canViewProfile: true, 
        canViewStories: true, 
        canViewActivity: true, 
        canViewFriends: true 
      };
    }

    const targetPrivacy = await getUserPrivacySettings(profileUserId);
    const visibility = targetPrivacy.profileVisibility;
    
    const areFriends = await checkIfFriends(viewerId, profileUserId);
    const isCommunityMember = viewerId !== null; // Logged in user

    const permissions = {
      canViewProfile: checkVisibilityPermission(visibility.whoCanSeeProfile, areFriends, isCommunityMember),
      canViewStories: checkVisibilityPermission(visibility.whoCanSeeStories, areFriends, isCommunityMember),
      canViewActivity: checkVisibilityPermission(visibility.whoCanSeeActivity, areFriends, isCommunityMember),
      canViewFriends: checkVisibilityPermission(visibility.whoCanSeeFriends, areFriends, isCommunityMember),
      canSeeOnlineStatus: visibility.showOnlineStatus,
      canSeeLastSeen: visibility.showLastSeen
    };

    return permissions;
  } catch (error) {
    console.error('Error checking profile view permission:', error);
    return { 
      canViewProfile: false, 
      canViewStories: false, 
      canViewActivity: false, 
      canViewFriends: false 
    };
  }
}

/**
 * Check if user can send message to another user
 * @param {string} fromUserId - User sending the message
 * @param {string} toUserId - User receiving the message
 * @returns {Object} Permission result
 */
export async function canSendMessage(fromUserId, toUserId) {
  try {
    if (fromUserId === toUserId) {
      return { allowed: false, reason: 'Cannot send message to yourself' };
    }

    const targetPrivacy = await getUserPrivacySettings(toUserId);
    const communicationSettings = targetPrivacy.communication;

    switch (communicationSettings.whoCanMessage) {
      case 'no_one':
        return { allowed: false, reason: 'This user is not accepting messages' };
      
      case 'friends_only':
        const areFriends = await checkIfFriends(fromUserId, toUserId);
        if (!areFriends) {
          return { allowed: false, reason: 'This user only accepts messages from friends' };
        }
        break;
      
      case 'everyone':
      default:
        // Allow message
        break;
    }

    return { allowed: true };
  } catch (error) {
    console.error('Error checking message permission:', error);
    return { allowed: false, reason: 'Unable to verify permissions' };
  }
}

/**
 * Check if user should appear in search results
 * @param {string} userId - User to check
 * @param {string} searcherId - User performing the search
 * @returns {boolean} Whether user should appear in search
 */
export async function shouldAppearInSearch(userId, searcherId) {
  try {
    if (userId === searcherId) return true;

    const userPrivacy = await getUserPrivacySettings(userId);
    return userPrivacy.searchability.allowInSearch;
  } catch (error) {
    console.error('Error checking search visibility:', error);
    return false;
  }
}

/**
 * Check if user should appear in friend suggestions
 * @param {string} userId - User to check
 * @param {string} suggesteeId - User receiving suggestions
 * @returns {boolean} Whether user should appear in suggestions
 */
export async function shouldAppearInSuggestions(userId, suggesteeId) {
  try {
    if (userId === suggesteeId) return false;

    const userPrivacy = await getUserPrivacySettings(userId);
    return userPrivacy.searchability.allowInSuggestions;
  } catch (error) {
    console.error('Error checking suggestion visibility:', error);
    return false;
  }
}

// Helper functions
function mergeWithDefaults(settings) {
  return deepMerge(DEFAULT_PRIVACY_SETTINGS, settings);
}

function deepMerge(target, source) {
  const result = { ...target };
  
  for (const key in source) {
    if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
      result[key] = deepMerge(target[key] || {}, source[key]);
    } else {
      result[key] = source[key];
    }
  }
  
  return result;
}

function checkVisibilityPermission(setting, areFriends, isCommunityMember) {
  switch (setting) {
    case 'everyone': return true;
    case 'community': return isCommunityMember;
    case 'friends_only': return areFriends;
    case 'private': return false;
    default: return false;
  }
}

async function checkIfFriends(userId1, userId2) {
  try {
    const user1Doc = await getDoc(doc(db, 'users', userId1));
    if (!user1Doc.exists()) return false;
    
    const user1Friends = user1Doc.data().friends || [];
    return user1Friends.includes(userId2);
  } catch (error) {
    console.error('Error checking friendship:', error);
    return false;
  }
}

async function checkMutualFriends(userId1, userId2) {
  try {
    const [user1Doc, user2Doc] = await Promise.all([
      getDoc(doc(db, 'users', userId1)),
      getDoc(doc(db, 'users', userId2))
    ]);

    if (!user1Doc.exists() || !user2Doc.exists()) return false;

    const user1Friends = user1Doc.data().friends || [];
    const user2Friends = user2Doc.data().friends || [];

    // Check if they have any mutual friends
    return user1Friends.some(friendId => user2Friends.includes(friendId));
  } catch (error) {
    console.error('Error checking mutual friends:', error);
    return false;
  }
}
